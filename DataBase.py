# подключения к базе PG для внесения информации сканированных файлах и страницах

import asyncpg
import asyncio
import os
import logging
from dotenv import dotenv_values

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
config = dotenv_values(".env")
DB_USER = config.get("PG_USER", "")
DB_PASSWORD = config.get("PG_PASSWORD", "")
DB_HOST = config.get("PG_HOST_LOCAL", "")
DB_PORT = config.get("PG_PORT", "")
DB_NAME = config.get("PG_DBNAME", "")

SIMILAR = "CREATE EXTENSION IF NOT EXISTS pg_trgm;"  # Создание расширения pg_trgm для поддержки функций похожести текста

if not DB_USER or not DB_PASSWORD or not DB_HOST or not DB_PORT or not DB_NAME:
    raise ValueError("Не все переменные окружения для базы данных указаны в файле .env")

TABLE_NAME = "t_scan_documents"
TABLE_NAME_RAW = "t_scan_documents_raw"


async def create_pool():
    try:
        pool = await asyncpg.create_pool(
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
        )
        return pool
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")
        # return None


# создаем таблицу
async def create_tables(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
                    id SERIAL PRIMARY KEY,
                    doc_type varchar(255) NULL,
                    doc_date date NULL,
                    doc_number varchar(255) NULL,
                    buyer_name varchar(255) NULL,
                    buyer_code varchar(255) NULL,
                    page_type INT NULL,
                    invoices_numbers jsonb NULL,
                    amount_with_vat numeric(15,2) NULL DEFAULT 0,
                    rows_list jsonb NULL,
                    external_id INT NOT NULL,
                    page_number INT NULL,
                    page INT NULL,
                    sort INT NULL,
                    date_from_1c timestamp(0) NULL,
                    file_name varchar(255) NULL,
                    full_path varchar(255) NULL,
                    description TEXT NULL,
                    thinking_content TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE (external_id)
                );

                COMMENT ON TABLE {TABLE_NAME} IS 'Таблица для хранения информации о сканерованных документах';
                COMMENT ON COLUMN {TABLE_NAME}.full_path IS 'Полный путь к файлу';
                COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'Тип';
                COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер';
                COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата1C';
                COMMENT ON COLUMN {TABLE_NAME}.page IS 'номер страницы в сортировке';
                COMMENT ON COLUMN {TABLE_NAME}.sort IS 'сортировка внутир документа';
                COMMENT ON COLUMN {TABLE_NAME}.description IS 'текст страницы';
                COMMENT ON COLUMN {TABLE_NAME}.created_at IS 'Дата создания записи';
                COMMENT ON COLUMN {TABLE_NAME}.buyer_code IS 'ОКПО';
                COMMENT ON COLUMN {TABLE_NAME}.buyer_name IS 'Покупатель';
                COMMENT ON COLUMN {TABLE_NAME}.date_from_1c IS 'Дата из 1С';
                COMMENT ON COLUMN {TABLE_NAME}.page_type IS 'тип страницы';
                COMMENT ON COLUMN {TABLE_NAME}.rows_list IS 'Список строк на странице';
                COMMENT ON COLUMN {TABLE_NAME}.external_id IS 'Внешний id';
                COMMENT ON COLUMN {TABLE_NAME}.page_number IS 'Номер страницы в исходном файле';
                COMMENT ON COLUMN {TABLE_NAME}.invoices_numbers IS 'Список номеров связанных ВН';
                COMMENT ON COLUMN {TABLE_NAME}.thinking_content IS 'логика мышления';
                COMMENT ON COLUMN {TABLE_NAME}.file_name IS 'Имя файла';
                COMMENT ON COLUMN {TABLE_NAME}.amount_with_vat IS 'Сумма с НДС';
                """
            )
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def fn_t_scan_documents(pool):
    """Создание оптимизированного триггера и вспомогательных функций"""
    try:
        async with pool.acquire() as conn:
            async with conn.transaction():
                # Удаляем старые функции
                await conn.execute("DROP FUNCTION IF EXISTS fn_t_scan_documents() CASCADE;")
                await conn.execute("DROP FUNCTION IF EXISTS normalize_text_field(TEXT, TEXT) CASCADE;")
                await conn.execute("DROP FUNCTION IF EXISTS normalize_json_array(JSONB) CASCADE;")
                await conn.execute("DROP FUNCTION IF EXISTS normalize_invoices_numbers(JSONB) CASCADE;")
                await conn.execute("DROP FUNCTION IF EXISTS clean_doc_number(TEXT) CASCADE;")

                # Создаем вспомогательные функции
                await conn.execute(r"""
                    CREATE OR REPLACE FUNCTION normalize_text_field(input_text TEXT, field_type TEXT DEFAULT 'general')
                    RETURNS TEXT
                    LANGUAGE plpgsql
                    IMMUTABLE
                    AS $$
                    BEGIN
                        IF input_text IS NULL OR NULLIF(TRIM(input_text), '') IS NULL THEN
                            RETURN NULL;
                        END IF;

                        -- Базовая очистка
                        input_text := REGEXP_REPLACE(input_text, '[[:cntrl:]]', '', 'g');
                        input_text := NULLIF(TRIM(input_text), '');

                        IF input_text IS NULL THEN
                            RETURN NULL;
                        END IF;

                        -- Специфичная обработка по типу поля
                        CASE field_type
                            WHEN 'buyer_name' THEN
                                RETURN REGEXP_REPLACE(UPPER(input_text), '[^А-ЯІЇЄҐ0-9A-Z"" ]', '', 'g');
                            WHEN 'buyer_code' THEN  
                                RETURN input_text;
                            WHEN 'doc_number' THEN
                                RETURN input_text;
                            ELSE
                                RETURN UPPER(input_text);
                        END CASE;
                    END;
                    $$;
                """)

                await conn.execute(r"""
                    CREATE OR REPLACE FUNCTION normalize_json_array(input_json JSONB)
                    RETURNS JSONB
                    LANGUAGE plpgsql  
                    IMMUTABLE
                    AS $$
                    BEGIN
                        IF input_json IS NULL OR input_json = 'null'::jsonb THEN
                            RETURN '[]'::jsonb;
                        END IF;

                        IF jsonb_typeof(input_json) != 'array' OR jsonb_array_length(input_json) = 0 THEN
                            RETURN '[]'::jsonb;
                        END IF;

                        RETURN input_json;
                    END;
                    $$;
                """)

                await conn.execute(r"""
                    CREATE OR REPLACE FUNCTION normalize_invoices_numbers(input_json JSONB)
                    RETURNS JSONB
                    LANGUAGE plpgsql
                    IMMUTABLE  
                    AS $$
                    BEGIN
                        input_json := normalize_json_array(input_json);

                        IF jsonb_array_length(input_json) = 0 THEN
                            RETURN input_json;
                        END IF;

                        -- Преобразование и сортировка уникальных значений
                        RETURN (
                            SELECT jsonb_agg(DISTINCT elem::integer ORDER BY elem::integer)
                            FROM jsonb_array_elements_text(input_json) AS elem
                            WHERE elem ~ '^\d+$'  -- Только числовые значения
                        );
                    END;
                    $$;
                """)

                await conn.execute(r"""
                    CREATE OR REPLACE FUNCTION clean_doc_number(input_number TEXT)
                    RETURNS TEXT  
                    LANGUAGE plpgsql
                    IMMUTABLE
                    AS $$
                    BEGIN
                        IF input_number IS NULL THEN
                            RETURN NULL;
                        END IF;

                        -- Удаление "3000" и извлечение только цифр
                        input_number := CASE 
                            WHEN input_number LIKE '%3000%' THEN REPLACE(input_number, '3000', '')
                            ELSE input_number
                        END;

                        -- Извлечение цифровой части
                        input_number := REGEXP_REPLACE(input_number, '^.*?(\d+)\D*$', '\1');

                        -- Удаление ведущих нулей  
                        input_number := REGEXP_REPLACE(input_number, '^0+(\d+)$', '\1');

                        RETURN NULLIF(input_number, '');
                    END;
                    $$;
                """)

                # Создаем основной триггер
                await conn.execute(r"""
CREATE OR REPLACE FUNCTION fn_t_scan_documents()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
DECLARE
    v_current_id BIGINT;
    v_raw_data RECORD;
    v_source_doc RECORD;
    v_buyer_info RECORD;
    source_doc_date DATE;
    source_doc_number TEXT;
    source_buyer_name TEXT;
    source_buyer_code TEXT;
    v_invoice_number TEXT;
BEGIN
    -- Установка ID текущей записи
    v_current_id := CASE WHEN TG_OP = 'UPDATE' THEN OLD.id ELSE NULL END;

    -- 1. НОРМАЛИЗАЦИЯ ТЕКСТОВЫХ ПОЛЕЙ
    NEW.buyer_name := normalize_text_field(NEW.buyer_name, 'buyer_name');
    NEW.buyer_code := normalize_text_field(NEW.buyer_code, 'buyer_code'); 
    NEW.doc_number := normalize_text_field(NEW.doc_number, 'doc_number');

    -- 2. ПОЛУЧЕНИЕ ДАННЫХ ИЗ RAW ТАБЛИЦЫ
    SELECT full_path, page_number, description, file_name
    INTO v_raw_data
    FROM t_scan_documents_raw 
    WHERE id = NEW.external_id;

    IF FOUND THEN
        NEW.full_path := COALESCE(NULLIF(NEW.full_path,''), v_raw_data.full_path);
        NEW.page_number := COALESCE(NEW.page_number, v_raw_data.page_number);
        NEW.description := COALESCE(NULLIF(NEW.description,''), v_raw_data.description);
        NEW.file_name := COALESCE(NULLIF(NEW.file_name,''), v_raw_data.file_name);
    END IF;

    -- 3. ОБРАБОТКА ПОЛЕЙ FILE_NAME И JSON
    IF NEW.full_path IS NOT NULL AND NEW.file_name IS NULL THEN
        NEW.file_name := regexp_replace(NEW.full_path, '^.*[\\/]', '');
    END IF;

    NEW.rows_list := normalize_json_array(NEW.rows_list);
    NEW.invoices_numbers := normalize_invoices_numbers(NEW.invoices_numbers);

    -- 4. ЗАПОЛНЕНИЕ ДАННЫХ ИЗ СВЯЗАННЫХ ДОКУМЕНТОВ
    WITH document_relations AS (
        SELECT 
            doc_date, doc_number, buyer_name, buyer_code,
            ROW_NUMBER() OVER (
                ORDER BY 
                    CASE 
                        WHEN doc_type = 'ТТН' AND page_type = 1 THEN 1
                        WHEN doc_type = 'ВН' THEN 2
                        ELSE 3
                    END,
                    created_at DESC
            ) as rn
        FROM t_scan_documents
        WHERE file_name = NEW.file_name 
          AND id IS DISTINCT FROM v_current_id
          AND (
              -- Условие 1: ТТН стр.1 -> ВН
              (
                NEW.doc_type = 'ТТН' 
                AND NEW.page_type = 1 
                AND doc_type = 'ВН' 
                AND COALESCE(NEW.invoices_numbers, '[]'::jsonb) <> '[]'::jsonb
                AND EXISTS (
                    SELECT 1 
                    FROM jsonb_array_elements_text(NEW.invoices_numbers) elem
                    WHERE elem = doc_number
                )
              )
              
              OR
              -- Условие 2: ТТН стр.2,3,999 -> ТТН стр.1
              (
                NEW.doc_type = 'ТТН' 
                AND NEW.page_type IN (2, 3, 999) 
                AND doc_type = 'ТТН' 
                AND page_type = 1
                AND (
                    (
                        COALESCE(NEW.invoices_numbers, '[]'::jsonb) <> '[]'::jsonb 
                        AND COALESCE(invoices_numbers, '[]'::jsonb) <> '[]'::jsonb
                        AND NEW.invoices_numbers = invoices_numbers
                    )
                    OR
                    (
                        NEW.doc_number IS NOT NULL 
                        AND doc_number IS NOT NULL 
                        AND doc_number::text = NEW.doc_number::text
                    )
                )
              )
              
              OR
              -- Условие 3: ТТН стр.2,3,999 -> ВН
              (
                NEW.doc_type = 'ТТН' 
                AND NEW.page_type IN (2, 3, 999) 
                AND doc_type = 'ВН' 
                AND COALESCE(NEW.invoices_numbers, '[]'::jsonb) <> '[]'::jsonb
                AND EXISTS (
                    SELECT 1 
                    FROM jsonb_array_elements_text(NEW.invoices_numbers) elem
                    WHERE elem = doc_number
                )
              )
              
              OR
              -- Условие 4: ВН -> ТТН стр.1
              (
                NEW.doc_type = 'ВН' 
                AND NEW.doc_number IS NOT NULL
                AND doc_type = 'ТТН' 
                AND page_type = 1
                AND COALESCE(invoices_numbers, '[]'::jsonb) <> '[]'::jsonb
                AND EXISTS (
                    SELECT 1 
                    FROM jsonb_array_elements_text(invoices_numbers) elem
                    WHERE elem = NEW.doc_number
                )
              )
              
              OR
              -- Условие 5: TTH -> ТТН стр.1
              (
                NEW.doc_type = 'TTH' 
                AND doc_type = 'ТТН' 
                AND page_type = 1
                AND (
                    (
                        COALESCE(NEW.invoices_numbers, '[]'::jsonb) <> '[]'::jsonb 
                        AND COALESCE(invoices_numbers, '[]'::jsonb) <> '[]'::jsonb
                        AND NEW.invoices_numbers = invoices_numbers
                    )
                    OR
                    (
                        NEW.doc_number IS NOT NULL 
                        AND doc_number IS NOT NULL 
                        AND doc_number::text = NEW.doc_number::text
                    )
                )
              )
          )
    )
    SELECT doc_date, doc_number, buyer_name, buyer_code
    INTO v_source_doc  
    FROM document_relations
    WHERE rn = 1;

    -- Заполнение полей из найденного документа
    IF FOUND THEN
        NEW.doc_date := COALESCE(NEW.doc_date, v_source_doc.doc_date);
        NEW.doc_number := COALESCE(NEW.doc_number, v_source_doc.doc_number);  
        NEW.buyer_name := COALESCE(NEW.buyer_name, v_source_doc.buyer_name);
        NEW.buyer_code := COALESCE(NEW.buyer_code, v_source_doc.buyer_code);
    END IF;

    -- 5. ГАРАНТИЯ ДЛЯ СТРАНИЦ 3/999: ДОБАВЛЕН ПРЯМОЙ ПОИСК ПО ДОКУМЕНТАМ
    IF NEW.doc_type = 'ТТН' AND NEW.page_type IN (3, 999) 
        AND COALESCE(NEW.invoices_numbers, '[]'::jsonb) <> '[]'::jsonb 
        AND (NEW.doc_date IS NULL OR NEW.buyer_name IS NULL OR NEW.doc_number IS NULL) THEN
        
        -- Берем первый номер из массива
        v_invoice_number := (NEW.invoices_numbers->>0)::TEXT;
        
        -- Прямой поиск ТТН стр.1 с таким номером
        SELECT doc_date, doc_number, buyer_name, buyer_code
        INTO source_doc_date, source_doc_number, source_buyer_name, source_buyer_code
        FROM t_scan_documents
        WHERE file_name = NEW.file_name 
          AND doc_type = 'ТТН'
          AND page_type = 1
          AND doc_number = v_invoice_number
          AND id IS DISTINCT FROM v_current_id
        ORDER BY created_at DESC
        LIMIT 1;

        IF FOUND THEN
            NEW.doc_date := COALESCE(NEW.doc_date, source_doc_date);
            NEW.doc_number := COALESCE(NEW.doc_number, source_doc_number);
            NEW.buyer_name := COALESCE(NEW.buyer_name, source_buyer_name);
            NEW.buyer_code := COALESCE(NEW.buyer_code, source_buyer_code);
        ELSE
            -- Если не нашли ТТН, ищем ВН
            SELECT doc_date, doc_number, buyer_name, buyer_code
            INTO source_doc_date, source_doc_number, source_buyer_name, source_buyer_code
            FROM t_scan_documents
            WHERE file_name = NEW.file_name 
              AND doc_type = 'ВН'
              AND doc_number = v_invoice_number
              AND id IS DISTINCT FROM v_current_id
            ORDER BY created_at DESC
            LIMIT 1;

            IF FOUND THEN
                NEW.doc_date := COALESCE(NEW.doc_date, source_doc_date);
                NEW.doc_number := COALESCE(NEW.doc_number, source_doc_number);
                NEW.buyer_name := COALESCE(NEW.buyer_name, source_buyer_name);
                NEW.buyer_code := COALESCE(NEW.buyer_code, source_buyer_code);
            END IF;
        END IF;
    END IF;

    -- 6. Логика для ТТН page_type = 1 (без изменений)
    IF NEW.doc_type = 'ТТН' AND NEW.page_type = 1 AND COALESCE(NEW.invoices_numbers, '[]'::jsonb) <> '[]'::jsonb THEN
        -- Попытка 1: совпадение doc_number
        IF NEW.doc_date IS NULL OR NEW.doc_number IS NULL OR NEW.buyer_name IS NULL OR NEW.buyer_code IS NULL THEN
            SELECT doc_date, doc_number, buyer_name, buyer_code
            INTO source_doc_date, source_doc_number, source_buyer_name, source_buyer_code
            FROM t_scan_documents
            WHERE doc_type = 'ВН'
              AND doc_number = NEW.doc_number
              AND file_name = NEW.file_name
              AND id IS DISTINCT FROM v_current_id
            ORDER BY created_at DESC
            LIMIT 1;

            IF FOUND THEN
                NEW.doc_date := COALESCE(NEW.doc_date, source_doc_date);
                NEW.doc_number := COALESCE(NEW.doc_number, source_doc_number);
                NEW.buyer_name := COALESCE(NEW.buyer_name, source_buyer_name);
                NEW.buyer_code := COALESCE(NEW.buyer_code, source_buyer_code);
            END IF;
        END IF;

        -- Попытка 2: поиск в массиве
        IF (NEW.doc_date IS NULL OR NEW.doc_number IS NULL OR NEW.buyer_name IS NULL OR NEW.buyer_code IS NULL) THEN
            SELECT doc_date, doc_number, buyer_name, buyer_code
            INTO source_doc_date, source_doc_number, source_buyer_name, source_buyer_code
            FROM t_scan_documents
            WHERE doc_type = 'ВН'
              AND EXISTS (
                  SELECT 1 
                  FROM jsonb_array_elements_text(NEW.invoices_numbers) elem
                  WHERE elem = doc_number
              )
              AND file_name = NEW.file_name
              AND id IS DISTINCT FROM v_current_id
            ORDER BY created_at DESC
            LIMIT 1;

            IF FOUND THEN
                NEW.doc_date := COALESCE(NEW.doc_date, source_doc_date);
                NEW.doc_number := COALESCE(NEW.doc_number, source_doc_number);
                NEW.buyer_name := COALESCE(NEW.buyer_name, source_buyer_name);
                NEW.buyer_code := COALESCE(NEW.buyer_code, source_buyer_code);
            END IF;
        END IF;
    END IF;

    -- 7. НОРМАЛИЗАЦИЯ BUYER_CODE И BUYER_NAME
    WITH buyer_data AS (
        SELECT buyer_code, buyer_name, COUNT(*) as usage_count
        FROM t_scan_documents
        WHERE (NEW.buyer_name IS NOT NULL AND buyer_name = NEW.buyer_name AND buyer_code IS NOT NULL)
           OR (NEW.buyer_code IS NOT NULL AND buyer_code = NEW.buyer_code AND buyer_name IS NOT NULL)
        AND id IS DISTINCT FROM v_current_id
        GROUP BY buyer_code, buyer_name
        ORDER BY usage_count DESC, buyer_code
        LIMIT 1
    )
    SELECT buyer_code, buyer_name 
    INTO v_buyer_info
    FROM buyer_data;

    IF FOUND THEN
        NEW.buyer_code := COALESCE(NEW.buyer_code, v_buyer_info.buyer_code);
        NEW.buyer_name := COALESCE(NEW.buyer_name, v_buyer_info.buyer_name);
    END IF;

    -- 8. ФИНАЛЬНАЯ ОБРАБОТКА DOC_NUMBER
    NEW.doc_number := clean_doc_number(NEW.doc_number);

    RETURN NEW;

EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Trigger fn_t_scan_documents failed for doc_type=%, page_type=%, file=%: %', 
            NEW.doc_type, NEW.page_type, NEW.file_name, SQLERRM;
END;
$function$;    
    """)

    except Exception as e:
        raise ValueError(f"ERROR DataBase/fn_t_scan_documents: {e}")


async def create_indexes(pool, table_name: str=TABLE_NAME, table_name_raw: str=TABLE_NAME_RAW):
    """
    Создает индексы для оптимизации работы триггера.
    Каждый индекс выполняется отдельно из-за CONCURRENTLY.
    """
    index_commands = [
        # Основной составной индекс для связывания документов
        f"""CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_file_type_page 
        ON {table_name} (file_name, doc_type, page_type) 
        WHERE doc_type IN ('ТТН', 'ВН', 'ПН');""",

        # GIN индекс для JSONB операций
        f"""CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_invoices_gin 
        ON {table_name} USING GIN (invoices_numbers);""",

        # Индексы для поиска покупателей
        f"""CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_buyer_name 
        ON {table_name} (buyer_name) 
        WHERE buyer_name IS NOT NULL;""",

        f"""CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_buyer_code
        ON {table_name} (buyer_code)
        WHERE buyer_code IS NOT NULL;""",

        # Индекс для связи с raw таблицей
        f"""CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_raw_id
        ON {table_name_raw} (id);""",

        # Комбинированный индекс для buyer поиска
        f"""CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_buyer_combined
        ON {table_name} (buyer_code, buyer_name) 
        WHERE buyer_code IS NOT NULL AND buyer_name IS NOT NULL;""",
    ]

    results = []
    async with pool.acquire() as conn:
        for i, command in enumerate(index_commands, 1):
            try:
                await conn.execute(command)
                results.append(f"✓ Index {i} created successfully")
            except Exception as e:
                results.append(f"✗ Index {i} failed: {str(e)}")
                print(f"Could not execute command {i}: {command.strip()}")
                print(f"Error: {e}\n")

    return results


async def create_function_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(r"""
            CREATE OR REPLACE FUNCTION public.fn_extract_file_name()
             RETURNS trigger
             LANGUAGE plpgsql
            AS $function$
                BEGIN
                    IF new.full_path IS NOT NULL THEN
                        new.file_name := (SELECT regexp_replace(new.full_path,  '^.*[\\/]', ''));
                        new.file_name := (SELECT split_part(new.full_path,  '\', -1));
                    ELSE
                        new.file_name := NULL;
                    END IF;
            
                    new.description = trim(replace(new.description,'  ',' '));
            
                    RETURN NEW;
                END;
            $function$
            ;
        """)
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_function_base: {e}")


async def create_trigger(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                CREATE TRIGGER trg_{TABLE_NAME}_bfr
                BEFORE INSERT OR UPDATE ON {TABLE_NAME}
                FOR EACH ROW
                EXECUTE PROCEDURE fn_t_scan_documents();
    """
            )
    except asyncpg.exceptions.DuplicateObjectError:
        pass
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_trigger: {e}")


async def create_trigger_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                CREATE TRIGGER trg_{TABLE_NAME_RAW}_bfr
                BEFORE INSERT OR UPDATE ON {TABLE_NAME_RAW}
                FOR EACH ROW
                EXECUTE PROCEDURE fn_extract_file_name();
            """
            )
    except asyncpg.exceptions.DuplicateObjectError:
        pass
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_trigger: {e}")


async def insert_from_extract_info(pool, json_string, full_path, path, date_parse):
    """
    Добавляет данные из формата extract_info в таблицу

    Args:
        pool: Пул соединений с базой данных
        json_string: Данные в формате extract_info
        full_path: Имя файла
        path: Путь к файлу
        date_parse: Дата парсинга
    """
    try:
        data_to_insert = []

        # Пытаемся распарсить JSON, если json_string вернул строку
        extract_data = {}
        if isinstance(json_string, str):
            import json
            try:
                logger.info(f"Получена строка JSON длиной {len(json_string)} символов")
                json_string = json_string.strip()

                # Обрабатываем различные форматы JSON
                if json_string.startswith('```json'):
                    logger.info("Обнаружен формат markdown JSON")
                    json_string = json_string[7:]
                if json_string.endswith('```'):
                    json_string = json_string[:-3]
                if json_string.startswith('"'):
                    logger.info("Обнаружены внешние кавычки")
                    json_string = json_string[1:]
                if json_string.endswith('"'):
                    json_string = json_string[:-1]

                # Выводим первые 200 символов для отладки
                logger.info(f"Подготовленная JSON строка (первые 200 символов): {json_string[:200]}...")

                # Преобразуем строку JSON в словарь Python
                extract_data = json.loads(json_string)
                logger.info(f"JSON успешно преобразован в словарь: {list(extract_data.keys())}")
            except json.JSONDecodeError as e:
                logger.error(f"Не удалось распарсить JSON {json_string} из extract_info: {e}")
                logger.error(f"Первые 200 символов строки: {json_string[:200]}...")
                logger.error(f"Последние 200 символов строки: {json_string[-200:] if len(json_string) > 200 else json_string}")
        elif isinstance(json_string, dict):
            logger.info("Получен словарь вместо строки JSON")
            extract_data = json_string

        # Проверяем различные форматы данных, которые может вернуть extract_info
        if "doc" in extract_data and isinstance(extract_data["doc"], list):
            for doc in extract_data["doc"]:
                # Преобразуем invoices_numbers в строку, если это список
                invoices_str = None
                if "invoices_numbers" in doc and isinstance(doc["invoices_numbers"], list):
                    invoices_str = ",".join(map(str, doc["invoices_numbers"]))

                # Для каждой страницы в rows_list создаем запись
                for page_num in doc.get("rows_list", []):
                    # Создаем doc_key из типа документа и номера
                    doc_key = f"{doc.get('doc_type', '')}{doc.get('doc_number', '')}"

                    # Формируем кортеж данных для вставки
                    record = (
                        doc.get("doc_type"),
                        doc.get("doc_date"),
                        str(doc.get("doc_number")),
                        str(doc.get("page_type")),
                        page_num,
                        None,  # sort
                        full_path,
                        None,  # description
                        path,
                        date_parse,
                        doc_key,
                        None,  # date_from_1c
                        doc.get("buyer_name"),
                        str(doc.get("buyer_code")),
                        invoices_str,
                        doc.get("ID", 0)  # external_id
                    )
                    data_to_insert.append(record)

        # Проверяем другой возможный формат данных
        elif "documents" in extract_data and isinstance(extract_data["documents"], list):
            for doc in extract_data["documents"]:
                # Создаем doc_key из типа документа и номера
                doc_key = f"{doc.get('doc_type', '')}{doc.get('doc_number', '')}"

                # Получаем информацию о покупателе
                buyer_name = None
                buyer_code = None
                if "buyer" in extract_data:
                    buyer_name = extract_data["buyer"].get("name")
                    buyer_code = extract_data["buyer"].get("code")

                # Формируем кортеж данных для вставки
                record = (
                    doc.get("doc_type"),
                    doc.get("doc_date"),
                    str(doc.get("doc_number")),
                    extract_data.get("page_type"),
                    doc.get("page", 0),  # используем page из документа или 0
                    None,  # sort
                    full_path,
                    None,  # description
                    path,
                    date_parse,
                    doc_key,
                    None,  # date_from_1c
                    buyer_name,
                    str(buyer_code) if buyer_code else None,
                    None,  # invoices_numbers
                    doc.get("ID", 0)  # external_id
                )
                data_to_insert.append(record)

        if data_to_insert:
            async with pool.acquire() as conn:
                # Счетчики для статистики
                inserted_count = 0
                updated_count = 0

                for record in data_to_insert:
                    # Проверяем, существует ли запись с таким external_id
                    external_id = record[-1]  # Последний элемент в кортеже - external_id
                    existing_record = await conn.fetchrow(
                        f"SELECT id FROM {TABLE_NAME} WHERE external_id = $1",
                        external_id
                    )

                    if existing_record:
                        # Если запись существует, обновляем её
                        await conn.execute(
                            f"""
                            UPDATE {TABLE_NAME}
                            SET
                                doc_type = $1,
                                doc_date = CASE WHEN $2 IS NULL THEN NULL ELSE to_timestamp($2, 'DD.MM.YYYY')::date END,
                                doc_number = $3,
                                page_type = $4,
                                page = $5,
                                sort = $6,
                                full_path = $7,
                                description = $8,
                                path = $9,
                                date_parse = $10,
                                doc_key = $11,
                                date_from_1c = $12,
                                buyer_name = $13,
                                buyer_code = $14,
                                invoices_numbers = $15
                            WHERE external_id = $16
                            """,
                            *record
                        )
                        updated_count += 1
                    else:
                        # Если записи нет, вставляем новую
                        await conn.execute(
                            f"""
                            INSERT INTO {TABLE_NAME}
                            (
                                doc_type,
                                doc_date,
                                doc_number,
                                page_type,
                                page,
                                sort,
                                full_path,
                                description,
                                path,
                                date_parse,
                                doc_key,
                                date_from_1c,
                                buyer_name,
                                buyer_code,
                                invoices_numbers,
                                external_id
                            )
                            VALUES ($1,
                                   CASE WHEN $2 IS NULL THEN NULL ELSE to_timestamp($2, 'DD.MM.YYYY')::date END,
                                   $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                            """,
                            *record
                        )
                        inserted_count += 1

                logger.info(f"Обработано {len(data_to_insert)} записей из extract_info: добавлено {inserted_count}, обновлено {updated_count}")
                return True
        else:
            logger.warning("Нет данных для добавления из extract_info")
            return False

    except Exception as e:
        logger.error(f"Ошибка при вставке данных из extract_info: {e}")
        raise ValueError(f"Ошибка при вставке данных из extract_info: {e}")


async def check_pages_in_db(pool, correct_page_numbers: list):
    # проверяем все ли страницы занесены в базу
    try:
        async with pool.acquire() as conn:
            for i, page in enumerate(correct_page_numbers):
                doc_key = list(correct_page_numbers[i].values())[0]
                page_number = list(correct_page_numbers[i].values())[1]
                query = f"SELECT COUNT(*) FROM {TABLE_NAME} WHERE doc_key = $1 AND page = $2"
                result = await conn.fetchval(query, doc_key, page_number)
                if result == 0:
                    logger.error(
                        f"Страница {page_number} файла {doc_key} не занесена в базу"
                    )
                    logger.error(f"doc_key: {doc_key}, page_number: {page_number}")
                    raise ValueError("Не все страницы занесены в базу")

            return True

    except Exception as e:
        logger.error(f"Ошибка при проверке страниц в базе: {e}")
        raise ValueError(f"Ошибка при проверке страниц в базе: {e}")


async def create_table_scan_all_pages_in_one_cell(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS t_scan_all_pages_in_one_cell (
                    id SERIAL PRIMARY KEY,
                    doc_type varchar(255) NULL,
                    doc_date date NULL,
                    doc_number varchar(255) NULL,
                    buyer_name varchar(55) NULL,
                    buyer_code varchar(25) NULL,
                    page_numbers jsonb NULL,
                    invoices_numbers jsonb NULL,
                    amount_with_vat numeric(15,2) NULL DEFAULT 0,
                    reasoning_content TEXT NULL,
                    file_name varchar(255) NULL,
                    full_path varchar(255) NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                
                COMMENT ON TABLE t_scan_all_pages_in_one_cell IS 'Таблица для хранения информации о сканированных документах. Страницы объединены в ячейку.';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_type IS 'Тип';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_number IS 'Номер';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_date IS 'Дата';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.buyer_name IS 'Покупатель';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.buyer_code IS 'ОКПО';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.page_numbers IS 'Список номеров страниц';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.invoices_numbers IS 'Список номеров связанных ВН';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.amount_with_vat IS 'Сумма с НДС';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.reasoning_content IS 'логика мышления';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.file_name IS 'Имя файла';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.full_path IS 'Полный путь к файлу';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.description IS 'текст страницы';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.created_at IS 'Дата создания записи';
                """
                )
            
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def create_tables_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS {TABLE_NAME_RAW} (
                    id SERIAL PRIMARY KEY,
                    page_number INT NOT NULL,
                    file_name varchar(100) NOT NULL,
                    full_path varchar(255) NOT NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT file_name_page_number_unq UNIQUE (file_name, page_number)
                );

                COMMENT ON TABLE {TABLE_NAME_RAW} IS 'Текст отсканированных страниц';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.full_path IS 'Полный путь к файлу';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.page_number IS 'номер страницы';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.description IS 'текст страницы';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.created_at IS 'Дата создания записи';
                """
            )
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def insert_document_base(pool, data: list):
    try:
        async with pool.acquire() as conn:
            await conn.executemany(
                f"""
                INSERT INTO {TABLE_NAME_RAW}
                (
                    full_path,
                    page_number,
                    description,
                    created_at
                )
                VALUES ($1, $2, $3, now())
                ON CONFLICT (file_name, page_number) DO UPDATE
                SET
                    description = EXCLUDED.description,
                    page_number = EXCLUDED.page_number,
                    full_path = EXCLUDED.full_path,
                    created_at = now()
                """,
                data,
            )
    except Exception as e:
        logger.error(f"Ошибка при вставке данных в базу: {e}")
        raise ValueError(f"Ошибка при вставке данных в базу: {e}")


async def main_database(drop_raw_table=False):
    """
    Инициализирует базу данных

    Args:
        drop_raw_table: Если True, удаляет таблицу t_scan_documents_raw
    """
    pool = await create_pool()
    if pool:
        async with pool.acquire() as conn:
            # Устанавливаем SIMILAR
            await conn.execute(SIMILAR)
            await conn.execute("CREATE EXTENSION IF NOT EXISTS unaccent;")           

            # Удаляем таблицу t_scan_documents
            # await conn.execute(f"DROP TABLE IF EXISTS {TABLE_NAME};")
            await fn_t_scan_documents(pool)
            await create_function_base(pool)

            # Создаем таблицы
            await create_tables(pool)
            await create_indexes(pool)
            await create_tables_base(pool)
            
            # Создаем триггеры
            await create_trigger(pool)
            await create_trigger_base(pool)

            # Удаляем таблицу t_scan_documents_raw только если указано
            # if drop_raw_table:
            #     logger.warning("Удаление таблицы t_scan_documents_raw")
            # НЕ удалять
                # await conn.execute(f"DROP TABLE IF EXISTS {TABLE_NAME_RAW};")

        await pool.close()


async def process_raw_documents(pool):
    """
    Обрабатывает документы из таблицы t_scan_documents_raw

    Args:
        pool: Пул соединений с базой данных
    """
    try:
        # Импортируем функцию main из process_documents.py
        # Используем переданный пул соединений
        from process_documents import process_with_pool
        await process_with_pool(pool)
        return True
    except Exception as e:
        logger.error(f"Ошибка при обработке документов: {e}")
        return False


async def main_database_process():
    """Запускает процесс обработки документов из raw таблицы"""
    pool = await create_pool()
    if pool:
        try:
            await process_raw_documents(pool)
        finally:
            await pool.close()

if __name__ == "__main__":
    # Для тестирования можно запустить обработку документов
    # asyncio.run(main_database_process())
    asyncio.run(main_database())
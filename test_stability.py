import asyncio
import logging
import time
from pathlib import Path

# Импортируем функции из основного файла
from SavePDFPageAsImage import process_image_async, gemini_rate_limiter, force_reset_rate_limiter

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

async def test_single_file_multiple_times():
    """
    Тестирует обработку одного файла 100 раз для проверки стабильности
    """
    test_file = r"C:\Scan\All\AlreadyAddToDb\2025-07-01_Merge.pdf"
    test_page = 83
    
    if not Path(test_file).exists():
        logger.error(f"Тестовый файл не найден: {test_file}")
        return
    
    logger.info(f"=== ТЕСТ СТАБИЛЬНОСТИ: 100 запросов к одному файлу ===")
    logger.info(f"Файл: {test_file}")
    logger.info(f"Страница: {test_page}")
    
    successful_count = 0
    failed_count = 0
    timeout_count = 0
    start_time = time.time()
    
    for i in range(1, 101):
        try:
            logger.info(f"🔄 Тест {i}/100 - Начинаем обработку...")
            
            # Запускаем обработку с таймаутом 120 секунд
            result = await asyncio.wait_for(
                process_image_async(test_file, test_page),
                timeout=120
            )
            
            if result and result.get('description') is not None:
                successful_count += 1
                logger.info(f"✅ Тест {i}/100 - УСПЕШНО (описание: {len(str(result.get('description', '')))} символов)")
            else:
                failed_count += 1
                logger.warning(f"⚠️ Тест {i}/100 - НЕУДАЧА (пустой результат)")
                
        except asyncio.TimeoutError:
            timeout_count += 1
            logger.error(f"⏰ Тест {i}/100 - ТАЙМАУТ (120 сек)")
            # При таймауте принудительно очищаем rate limiter
            await force_reset_rate_limiter()
            
        except Exception as e:
            failed_count += 1
            logger.error(f"❌ Тест {i}/100 - ОШИБКА: {e}")
            
        # Показываем промежуточную статистику каждые 10 тестов
        if i % 10 == 0:
            elapsed = time.time() - start_time
            logger.info(f"📊 Промежуточная статистика после {i} тестов:")
            logger.info(f"   ✅ Успешно: {successful_count}")
            logger.info(f"   ⚠️ Неудачи: {failed_count}")
            logger.info(f"   ⏰ Таймауты: {timeout_count}")
            logger.info(f"   ⏱️ Время: {elapsed:.1f} сек")
            logger.info(f"   📈 Скорость: {i/elapsed*60:.1f} тестов/мин")
    
    # Финальная статистика
    total_time = time.time() - start_time
    logger.info(f"")
    logger.info(f"🏁 === ФИНАЛЬНЫЕ РЕЗУЛЬТАТЫ ТЕСТА ===")
    logger.info(f"📊 Всего тестов: 100")
    logger.info(f"✅ Успешно: {successful_count} ({successful_count}%)")
    logger.info(f"⚠️ Неудачи: {failed_count} ({failed_count}%)")
    logger.info(f"⏰ Таймауты: {timeout_count} ({timeout_count}%)")
    logger.info(f"⏱️ Общее время: {total_time:.1f} секунд")
    logger.info(f"📈 Средняя скорость: {100/total_time*60:.1f} тестов/мин")
    
    # Оценка стабильности
    if timeout_count == 0:
        logger.info(f"🎉 ОТЛИЧНО: Нет таймаутов - система стабильна!")
    elif timeout_count <= 5:
        logger.info(f"✅ ХОРОШО: Мало таймаутов ({timeout_count}) - система в основном стабильна")
    elif timeout_count <= 20:
        logger.warning(f"⚠️ СРЕДНЕ: Есть таймауты ({timeout_count}) - нужны улучшения")
    else:
        logger.error(f"❌ ПЛОХО: Много таймаутов ({timeout_count}) - система нестабильна")

async def main():
    """Главная функция теста"""
    try:
        await test_single_file_multiple_times()
    except KeyboardInterrupt:
        logger.info("Тест прерван пользователем")
    except Exception as e:
        logger.error(f"Критическая ошибка в тесте: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())

import asyncio
import logging
import os
from pathlib import Path
from typing import List, Dict, Any

# Асинхронные библиотеки для I/O
import aiofiles
import aiofiles.os
import asyncpg  # Замена для psycopg2

# CPU-bound библиотеки, которые будут выполняться в отдельных потоках
import fitz  # PyMuPDF
from PIL import Image

# --- ВАЖНО: Асинхронные версии ваших AI-функций ---
# Предполагается, что у вас есть или вы создадите асинхронные
# аналоги для этих функций. Я добавил суффикс _async.
from Gemini.GeminiAI import extract_entity_from_file_async
from Gemini.GoogleDocumentAI import google_ocr_documentai_async
from Mistral.MistalCorrectText import extract_data_from_text_by_mistral_async
from Mistral.MistralOCR import extract_ocr_data_by_mistral

from prompt import PROMPT_CLEAR_TEXT, PROMPT_OCR

# --- Константы и Настройки ---
MAX_IMAGE_SIZE_MB = 19
MIN_DPI = 300
MAX_DPI = 500
DPI_STEP = 25
MAX_PIXEL_DIMENSION = 4900
MAX_JPEG_SIZE = 9000

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# --- Параметры подключения к БД (из переменных окружения) ---
DB_PARAMS = {
    'host': os.getenv("PG_HOST_LOCAL", ""),
    'database': os.getenv("PG_DBNAME", ""),
    'user': os.getenv("PG_USER", ""),
    'password': os.getenv("PG_PASSWORD", ""),
    'port': int(os.getenv("PG_PORT", "5432"))
}


def get_file_extension(file_path: str) -> str:
    return Path(file_path).suffix.lower().lstrip('.')


def _save_pdf_page_as_image_sync_worker(input_path: str, page_number: int, dpi: int, jpeg_quality: int,
                                        max_size: int) -> str:
    """
    Синхронный воркер для CPU-bound операций. Выполняется в to_thread.
    """
    output_dir = "temp_image"
    os.makedirs(output_dir, exist_ok=True)

    doc = fitz.open(input_path)
    try:
        if not (1 <= page_number <= len(doc)):
            raise ValueError(f"Страница {page_number} отсутствует в документе (всего {len(doc)} стр.).")

        page = doc.load_page(page_number - 1)
        pix = page.get_pixmap(dpi=dpi)
        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

        if pix.width > max_size or pix.height > max_size:
            scale = min(max_size / pix.width, max_size / pix.height)
            new_size = (int(pix.width * scale), int(pix.height * scale))
            img = img.resize(new_size, Image.LANCZOS)
            logger.info(f"Изображение масштабировано до {new_size} из-за лимита {max_size}px")

        base_name = f"{Path(input_path).stem}_page_{page_number}.jpg"
        img_path = os.path.join(output_dir, base_name)
        img.save(img_path, format="JPEG", quality=jpeg_quality)
        return img_path
    finally:
        doc.close()


async def save_pdf_page_as_image_async(input_path: str, page_number: int, dpi: int = 300, jpeg_quality: int = 90,
                                       max_size: int = MAX_JPEG_SIZE) -> str | None:
    """
    Асинхронно извлекает страницу из PDF, выполняя тяжелые операции в отдельном потоке.
    """
    if not await aiofiles.os.path.exists(input_path):
        logger.error(f"Входной файл не существует: {input_path}")
        return None
    try:
        # Выносим всю синхронную, CPU-bound работу в отдельный поток
        img_path = await asyncio.to_thread(
            _save_pdf_page_as_image_sync_worker,
            input_path, page_number, dpi, jpeg_quality, max_size
        )
        logger.info(f"Сохранена страница {page_number} как изображение: {img_path}")
        return img_path
    except Exception as e:
        logger.error(f"Ошибка при сохранении страницы {page_number} из файла {input_path}: {e}", exc_info=True)
        return None


# ==============================================================================
# РАБОТА С БАЗОЙ ДАННЫХ (asyncpg)
# ==============================================================================

async def add_to_db_async(data: List[Dict[str, Any]]):
    """
    Асинхронно добавляет данные в базу данных, используя asyncpg.
    """
    if not data:
        return 0

    sql = """
        INSERT INTO t_scan_documents_raw (full_path, page_number, description, created_at)
        VALUES ($1, $2, $3, now())
        ON CONFLICT (file_name, page_number) DO UPDATE
        SET description = EXCLUDED.description,
            full_path = EXCLUDED.full_path,
            created_at = now();
    """
    values = [
        (
            doc.get('full_path'),
            doc.get('page_number'),
            doc.get('description')
        ) for doc in data
    ]

    conn = None
    try:
        conn = await asyncpg.connect(**DB_PARAMS)
        # executemany для массовой вставки
        result = await conn.executemany(sql, values)
        logger.info(f"Данные OCR ({len(values)} записей) успешно занесены в базу. Результат: {result}")
        return len(values)
    except Exception as e:
        logger.error(f"Ошибка при добавлении данных в БД: {e}", exc_info=True)
        return 0
    finally:
        if conn:
            await conn.close()


async def process_image_async(pdf_or_image_path: str, page_number: int) -> Dict[str, Any]:
    """
    Асинхронно обрабатывает одну страницу/изображение: сохраняет, делает OCR, очищает и пишет в БД.
    """
    data = {
        'full_path': pdf_or_image_path,
        'page_number': page_number,
        'description': None
    }
    temp_file_name = None

    try:
        ext = get_file_extension(pdf_or_image_path)
        if ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
            temp_file_name = pdf_or_image_path
        elif ext == 'pdf':
            temp_file_name = await save_pdf_page_as_image_async(pdf_or_image_path, page_number)
        else:
            logger.warning(f"Неподдерживаемый формат файла для OCR: {ext}")
            return {}

        if not temp_file_name:
            logger.error("Не удалось создать временный файл изображения.")
            return {}

        # --- Асинхронное выполнение OCR запросов ---
        # Можно даже запустить их параллельно для выбора лучшего
        ocr_tasks = [
            extract_entity_from_file_async(temp_file_name, PROMPT_OCR),
            # google_ocr_documentai_async(temp_file_name),
            # extract_ocr_data_by_mistral(temp_file_name) # Раскомментируйте при необходимости
        ]
        results = await asyncio.gather(*ocr_tasks, return_exceptions=True)

        # Выбираем лучший (самый длинный) результат из успешных
        ocr_text = ""
        for res in results:
            if isinstance(res, str) and len(res) > len(ocr_text):
                ocr_text = res

        logger.info(
            f"Лучший OCR результат для {Path(pdf_or_image_path).name} стр.{page_number} имеет длину {len(ocr_text)} символов.")

        # Очистка текста и запись в БД
        if ocr_text and len(ocr_text.strip()) > 100:  # Минимальный порог для осмысленного текста
            correct_ocr_text = await extract_data_from_text_by_mistral_async(ocr_text, PROMPT_CLEAR_TEXT)
            data['description'] = correct_ocr_text.strip() if correct_ocr_text else ocr_text.strip()
        else:
            data['description'] = ocr_text.strip() if ocr_text else None

        await add_to_db_async([data])
        return data

    finally:
        # Удаляем временный файл, если он был создан из PDF
        if temp_file_name and get_file_extension(pdf_or_image_path) == 'pdf' and await aiofiles.os.path.exists(
                temp_file_name):
            await aiofiles.os.remove(temp_file_name)


async def extract_pages_from_pdf_async(pdf_or_image_path: str, page_numbers: List[int] = None):
    """
    Асинхронно извлекает данные со страниц PDF или изображения.
    Обрабатывает страницы конкурентно (параллельно).
    """
    if not await aiofiles.os.path.exists(pdf_or_image_path):
        logger.error(f"Файл не найден: {pdf_or_image_path}")
        return

    if not page_numbers:
        # Если номера страниц не указаны, получаем их количество
        try:
            # Это быстрая синхронная операция, можно выполнить напрямую или в to_thread для 100% неблокировки
            doc = await asyncio.to_thread(fitz.open, pdf_or_image_path)
            total_pages = len(doc)
            doc.close()
            page_numbers = list(range(1, total_pages + 1))
        except Exception as e:
            logger.error(f"Не удалось прочитать PDF файл {pdf_or_image_path}: {e}")
            return

    # Создаем и запускаем задачи для каждой страницы конкурентно
    tasks = [process_image_async(pdf_or_image_path, pn) for pn in page_numbers]
    results = await asyncio.gather(*tasks)

    return {'doc': [res for res in results if res]}


async def extract_pdf_files_from_folder_async(folder_path: str):
    """
    Асинхронно находит все PDF в папке и обрабатывает их конкурентно.
    """
    pdf_files = []
    # os.walk - синхронный генератор, его можно оставить, т.к. он быстрый
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))

    # Запускаем обработку каждого файла как отдельную задачу
    tasks = [extract_pages_from_pdf_async(pdf_file) for pdf_file in pdf_files]
    await asyncio.gather(*tasks)


async def get_small_data_from_db_async():
    """
    Асинхронно отбирает записи из БД и запускает их на повторную обработку.
    """
    sql = """
        SELECT file_name, page_number
        FROM t_scan_documents_raw
        WHERE id in (
            SELECT external_id
            FROM t_scan_documents
            WHERE doc_type NOT IN ('ДОВІРЕНІСТЬ','ЕН') 
                AND external_id NOT IN (1231, 1426, 1465, 1491, 1724, 14711, 10126, 2719, 14817, 2833, 3008, 6052, 6238)
                AND (page_type  IS NULL  
                OR doc_date   IS NULL 
                OR doc_number IS NULL 
                OR buyer_name IS NULL 
                OR buyer_code IS NULL
                OR doc_type = 'ДРУГОЙ'
                )
        )
        ORDER BY file_name, page_number;
    """
    conn = None
    try:
        conn = await asyncpg.connect(**DB_PARAMS)
        rows = await conn.fetch(sql)
        logger.info(f"Найдено {len(rows)} записей для повторной обработки.")

        # Создаем задачи для повторной обработки
        tasks = []
        for row in rows:
            file_path = os.path.join(r"c:\Scan\All\AlreadyAddToDb", row['file_name'])
            if await aiofiles.os.path.exists(file_path):
                tasks.append(process_image_async(file_path, row['page_number']))
            else:
                logger.warning(f"Файл для повторной обработки не найден: {file_path}")

        # Запускаем все задачи конкурентно
        if tasks:
            await asyncio.gather(*tasks)

    except Exception as e:
        logger.error(f"Ошибка при получении данных из БД для переобработки: {e}", exc_info=True)
    finally:
        if conn:
            await conn.close()


# ==============================================================================
# ТОЧКА ВХОДА
# ==============================================================================

async def main():
    """Главная асинхронная функция."""
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    # --- Выберите один из вариантов для запуска ---

    # Вариант 1. Обработка всех pdf в папке с учетом вложенных.
    # logger.info("Запуск варианта 1: Обработка всех PDF в папке...")
    # folder_path = r"\\PrestigeProduct\Блок 2024\Епіцентр\07_ЛИПЕНЬ 2024р\ТТН"
    # await extract_pdf_files_from_folder_async(folder_path)

    # Вариант 2. Обработка одного pdf с указанием списка страниц.
    # logger.info("Запуск варианта 2: Обработка одного PDF с указанием страниц...")
    # page_numbers = [23]
    # pdf_file_path = r"C:\Scan\All\2025-07-03 Merge.pdf"
    # result = await extract_pages_from_pdf_async(pdf_file_path, page_numbers)
    # print(result)

    # Вариант 3. Обработка одного pdf БЕЗ указания списка страниц.
    # logger.info("Запуск варианта 3: Обработка всех страниц одного PDF...")
    # pdf_file_path = r"C:\Scan\All\2025-07-03 Merge.pdf"
    # result = await extract_pages_from_pdf_async(pdf_file_path)
    # print(result)

    # Вариант 4. Повторная обработка записей из БД.
    logger.info("Запуск варианта 4: Повторная обработка данных из БД...")
    await get_small_data_from_db_async()

    logger.info("Все операции завершены.")


if __name__ == "__main__":
    # Запускаем асинхронную программу
    asyncio.run(main())
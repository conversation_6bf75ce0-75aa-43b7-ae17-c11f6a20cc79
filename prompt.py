# Low Temperature (0.1) + Low Top-p (0.2) = Максимальная точность
# Low Temperature (0.1) + High Top-p (0.9) = Точность с небольшим разнообразием  
# High Temperature (0.8) + Low Top-p (0.2) = Контролируемая креативность
# High Temperature (0.8) + High Top-p (0.9) = Максимальное разнообразие

PROMPT_CLEAR_TEXT = """
Тебе дается OCR текст на украинском языке.
Ты - финансовый аналитик с многолетним опытом. Ты умеешь однозначно и точно определять по контексту с каким документом работаешь.
Проверь текст документа и найди в нем аномалии, не корректности, ошибки, незначащие повторы, несогласованности и т.д.
Проверь, чтобы конец текста был пустым или повторяющимися словами, символами.
Глубоко продумай каждую деталь и верни исправленный вариант текста, соблюдая формат и разметки.
** !!!БЕЗ КОММЕНТАРИЕВ, БЕЗ РАССУЖДЕНИЙ, БЕЗ ФАНТИЗАРОВАНИЯ, БЕЗ ПЕРЕВОДА И БЕЗ ДОПОЛНЕНИЯ!!! **
**ЕЩЕ РАЗ ДОБАВЛЯТЬ НЕЛЬЗЯ КОММЕНТАРИИ ТИПА: "Ось виправлений текст документа:", "Ісправлений текст", "Відредагований текст:" и т.д**
**ЯЗЫК НЕ МЕНЯТЬ!!!**
"""

PROMPT_AMOUNT_WITH_VAT = """
Тебе дается OCR текст на украинском языке.
Ты - финансовый аналитик с многолетним опытом. Ты умеешь однозначно и точно определять по контексту с каким документом работаешь.
Ты глубоко и детально разобрался во всех цифрах, числах, суммах данных тебе. И разобрался где суммы с НДС, где БЕЗ НДС, где количество. 
Где суммы написаны числом, а где прописью. Ты их глубоко проанализировал, многократно и детально перепроверил!!!.
Выведи мне сумму с НДС числом, в формате словаря: {'amount_with_vat':number}. 
Если нет суммы с НДС или ты не можешь его вычислить верни: {'amount_with_vat':0}.
** !!!БЕЗ КОММЕНТАРИЕВ, БЕЗ РАССУЖДЕНИЙ, БЕЗ ФАНТИЗАРОВАНИЯ, БЕЗ ПЕРЕВОДА И БЕЗ ДОПОЛНЕНИЯ!!! **
"""

PROMPT_OCR = """
  **Начало Промта для ИИ**

  **Задание:** Обработка отсканированного PDF-документа (язык: украинский).

  **Контекст:** Тебе предоставлен отсканированный PDF-файл на украинском языке. Качество сканирования может быть крайне низким: страницы могут быть перевернуты, содержать многочисленные артефакты, быть плохо выровненными, иметь значительный шум, искажения, "мусор" или очень размытый текст.

  **Твоя основная задача:** Максимально точно и полно извлечь ВЕСЬ **осмысленный и читаемый** текстовый контент из каждой страницы документа. При этом глубоко проанализировать его визуальную и логическую структуру для точного и форматированного воспроизведения в Markdown.
  
  **МАСИМАЛЬНО ТОЧНО СОХРАНИ СТРУКТУРУ ДОКУМЕНТА. ОСОБЕННО ТАБЛИЦ! ВАЖНО!!!**
  
  **Ключевые требования к выполнению:**

  1.  **Полнота извлечения читаемого текста:**
      *   Извлеки абсолютно весь **осмысленный и читаемый** текст с каждой непустой страницы.
      *   Не допускай пропусков **осмысленных** символов, слов, строк или целых блоков текста, которые можно однозначно распознать.
      *   Если страница не содержит никакого текста или значимых графических элементов (полностью пустая), выведи для такой страницы строго одну строку: `EMPTY_PAGE`.

  2.  **Точность и достоверность распознавания (с акцентом на осмысленность):**
      *   Обеспечь наивысшую возможную точность распознавания **осмысленных символов и слов** украинского алфавита.
      *   **Категорически запрещено:**
          *   Вставлять любые символы, которых нет в исходном документе.
          *   Генерировать или транскрибировать длинные, бессмысленные последовательности символов, которые являются результатом шума, артефактов сканирования или ошибочного распознавания "мусора" как текста.
          *   Если определенный фрагмент текста или символ абсолютно нечитаем, или распознается как длинная, нечитаемая, нелогичная или бессмысленная последовательность символов, приоритетом является **не включение этих бессмысленных данных** в вывод. Вместо этого, стремись к точному воспроизведению только осмысленного и читаемого контента.
          *   В случае, если содержимое ячейки таблицы или целого блока текста состоит исключительно из нечитаемого шума, эта часть содержимого должна быть опущена (т.е. ячейка или блок должны быть пустыми), а не заполнены случайными символами или длинными строками "мусора".

  3.  **Максимальное сохранение оригинального форматирования и структуры:**
      *   **Порядок элементов:** Слова, строки, абзацы и любые другие текстовые блоки должны следовать в том же порядке, что и в оригинальном документе. Не меняй их местами.
      *   **Переносы строк:** Точно воспроизводи оригинальные переносы строк. Не объединяй строки и не создавай новые искусственно.
      *   **Абзацы:** Сохраняй структуру абзацев, включая визуально определяемые отступы или пустые строки между ними.
      *   **Пробелы:** Воспроизводи пробелы между словами и символами так, как они представлены в оригинале.
      *   **Таблицы:** Если на странице присутствуют таблицы, ты **ОБЯЗАН** распознать их структуру (строки, столбцы, ячейки) и **корректно отформатировать** их с использованием Markdown-синтаксиса для таблиц. Содержимое ячеек должно быть точно передано, следуя правилам пункта 2.
      *   **Визуальные атрибуты текста (по возможности):** Если возможно надежно определить и воспроизвести в Markdown такие атрибуты, как **жирный шрифт**, *курсив* или `моноширинный текст`, сохрани их. Однако абсолютным приоритетом является точность извлечения осмысленного текста и сохранение общей структуры.

  4.  **Глубокий анализ текста (как основа для воспроизведения):**
      *   Твой "глубокий анализ" подразумевает тщательное понимание визуальной и логической структуры документа. Это включает анализ расположения текстовых блоков, выравнивания, наличия списков, заголовков, подзаголовков, колонок, а также шрифтовых особенностей (если они несут структурное значение), чтобы максимально точно воспроизвести их в Markdown.

  5.  **Формат вывода:**
      *   Весь извлеченный тобой контент для КАЖДОЙ СТРАНИЦЫ (или всего документа, если он короткий) должен быть представлен СТРОГО в виде единого блока Markdown.
      *   Твой ответ ДОЛЖЕН начинаться с ````markdown` и заканчиваться на ````. Никаких других символов, текста, объяснений или комментариев до или после этого блока быть не должно.
      *   **Таблицы внутри Markdown:** Убедись, что таблицы отформатированы правильно с использованием синтаксиса Markdown (заголовки, разделители, ячейки).

  6.  **Самопроверка перед выводом:**
      *   **Проверь, что твой вывод начинается с "```markdown" и заканчивается на "```". Если нет, исправь это.**
      *   **Убедись, что текст данного промта (этих инструкций) НЕ ПОЯВЛЯЕТСЯ в твоем итоговом ответе.** Твой ответ должен содержать только извлеченный из PDF текст (или `EMPTY_PAGE`) в указанном Markdown формате.
      *   Перепроверь, что ты не пропустил **осмысленный** текст, не добавил лишних (бессмысленных) символов и максимально сохранил оригинальный формат.

  **Язык документа:** Украинский. Все алгоритмы распознавания и анализа должны быть настроены на украинский язык.
  
  **ВАЖНО:**
    Проверь строки. Убери все последовательно идущие повторяющиеся символы, Которые НЕ несут смысловую нагрузку.
    Данные должны быть строго из данного тебе документа.
    Добавлять от себя, додумывать, фантазировать, думать что опечатка - ЗАПРЕЩЕНО!!!.
    Строго соблюдай структуру документа. Особенно таблиц! Пустые колонки удалять ЗАПРЕЩЕНО!
    
  **Приступай к выполнению.**
"""

GEMINI_AI_THINKS = """**Analyzing Accounting Data**

I've been working on extracting the critical data elements from the Ukrainian accounting documents, specifically focusing on identifying document types, dates, and numbers. I've encountered some hurdles in accurately capturing the referenced invoice numbers, particularly the TT numbers. I'm refining the pattern recognition to improve accuracy and ensure I capture all relevant information.


**Identifying Key Data Elements**

I'm currently focused on extracting the core data points from the Ukrainian accounting documents. This involves identifying the document type, date, and number, along with the buyer information. I'm building out the logic to handle multi-page documents, accurately determining their sequence. I'm also working to precisely pinpoint referenced invoice numbers, especially for TTN documents. I'm aiming for accuracy in extracting total amounts with VAT.


**Refining Document Boundaries**

I'm currently focused on precisely identifying the boundaries of each Ukrainian accounting document within the PDF. I'm leveraging keywords and page layout analysis to distinguish between \"Видаткова накладна\" and \"Товарно-транспортна накладна.\"  I've incorporated logic to handle multi-page documents, ensuring correct page sequencing.  I'm also working to extract the associated dates and buyer information.


**Structuring the Extraction Logic**

I'm currently focused on organizing the extraction logic.  I've built a framework to identify document types and crucial information.  I'm refining the process for handling multi-page documents like \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My approach now involves accurately capturing dates, buyer details, and referenced invoice numbers for each document type, alongside total amounts with VAT.


**Defining Extraction Strategies**

I've been working on crafting precise extraction strategies for the Ukrainian accounting documents. I'm focusing on parsing documents to detect keywords, document types, date, number, and buyer details, including EDRPOU codes. I've also incorporated logic to handle multi-page documents, including both \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My plans incorporate the identification of referenced invoice numbers and the calculation of VAT. I'm carefully constructing the logic to output the data in the requested JSON format. I also understand how the user wants to treat TTN and VN documents separately.


**Formulating Extraction Logic**

I've crafted an initial strategy to extract the required data, including document types, dates, and buyer details, with EDRPOU codes and referenced invoice numbers for TTNs. I've begun to tackle handling multi-page documents, specifically the \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My plans incorporate identifying dates, buyer details, and referenced invoice numbers for each document type, alongside total amounts with VAT. I understand how to format the data into the specified JSON output.


**Implementing PDF Data Extraction**

I'm currently focused on implementing the core logic for extracting the required data. I'm prioritizing the identification of document boundaries, buyer information, and document types. I'm meticulously parsing page content to capture dates, numbers, and referenced invoice numbers for each document. The planned output is JSON format, and I've started adding the calculated differences for TTNs. I'm using OCR results for pages 1-6 as input data.


**Developing Data Extraction Logic**

I'm currently focused on extracting information from the provided OCR data, particularly for the two documents across pages 1-6. I've designed logic to handle both \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My approach now includes extracting buyer details, dates, and referenced invoice numbers for each document. I'm also planning to calculate and output the amount difference for TTNs, and I understand the user's need to separate VN and TTN documents.


**Developing Data Structures**

I've constructed data structures to hold the extracted information. I'm focusing on defining how to represent different document types (VN and TTN), including their associated details like dates, buyer info, and referenced invoices. I'm also preparing for the calculation of the amount difference, and I am adding fields for page numbers.


**Developing Extraction Algorithms**

I've formulated a preliminary extraction strategy and identified key data:  types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents.


**Developing Robust Data Structures**

I'm currently focused on constructing detailed data structures to hold the extracted information, considering multi-page documents and referenced invoices. I've designed structures for \"Видаткова накладна\" (VN) and \"Товарно-транспортна накладна\" (TTN), including their respective dates, numbers, buyers, and page numbers. I am adding a method to calculate the amount difference for TTNs based on the referenced VNs and am making plans for the JSON format.


**Refining Data Extraction Strategies**

I'm focused on meticulously extracting all the details from the provided OCR data for those Ukrainian accounting documents. I've designed logic to handle both \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My approach now incorporates logic to extract buyer information, dates, and referenced invoice numbers for each document type and page number, along with the total amount with VAT. I'm also preparing to calculate and output the amount difference for TTNs based on the referenced VNs in JSON format.


**Implementing Data Structures**

I've established data structures to hold extracted information. For \"Видаткова накладна\" (VN) and \"Товарно-транспортна накладна\" (TTN), I've designed structures for their respective details, dates, numbers, buyers, page numbers, and referenced invoices for TTNs. I'm incorporating the calculation of the amount difference. I'm planning the output in JSON format.


**Optimizing Data Extraction**

I'm focused on developing the extraction logic, now incorporating handling multi-page \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" I've added a difference calculation for TTNs based on referenced VNs.  I am building on the understanding of extracting buyer information, dates, numbers, and page numbers. I've designed logic to provide the data in the requested JSON format, following the user's specific request.


**Developing Extraction Logic**

I've formulated a preliminary extraction strategy and identified key data: types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output. I've noted the difference between the VN and TTN documents, including specific page numbers for each. I'm also planning to calculate and output the amount difference for TTNs based on their corresponding VNs. I've created plans for how to format the data into the requested JSON output. I've also identified the user's specific requirement.


**Developing Output Formatting**

I've been working on extracting the required data, now meticulously adding page numbers to the JSON output. I've designed logic to handle both \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" My plans incorporate identifying dates, buyer details, and referenced invoice numbers for each document type, alongside total amounts with VAT. I'm carefully constructing the logic to output the data in the requested JSON format, with each page number listed.


**Developing Data Extraction**

I've formulated a preliminary strategy for extracting the required data, now incorporating multi-page support. I'm focusing on \"Видаткова накладна\" and \"Товарно-транспортна накладна.\"  My plans incorporate the extraction of dates, buyer details, page numbers, and referenced invoice numbers for each document type, alongside total amounts with VAT.  I am building on the difference calculation for TTNs, and formatting to the requested JSON. I am developing plans for each page number.


**Developing Extraction Algorithms**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.


**Developing Extraction Logic**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents, including specific page numbers for each. I'm also planning to calculate and output the amount difference for TTNs based on their corresponding VNs. I've created plans for how to format the data into the requested JSON output. I've also identified the user's specific requirement.


**Refining Data Extraction**

I've made considerable progress on parsing the provided OCR data, particularly focusing on pages 1-6. I've successfully implemented extraction logic for both \"Видаткова накладна\" and \"Товарно-транспортна накладна,\" including date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I've also added the total amounts with VAT to the JSON. I've corrected the row numbers, and I've also added support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4.


**Extracting and Processing Data**

I'm focused on extracting the necessary data. I've designed the extraction strategy to capture data on each page, including extracting document type, date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I have corrected the row numbers. Also, the plan incorporates the total amount with VAT. For pages 1, 2, 3, 4, 5, and 6, I am preparing the data for the calculation of the amount difference for TTNs based on the referenced VNs in JSON format. The correct row numbers for VN 8825, page 4, are 1, 2, and 4. I am in the final stages of JSON output formatting.


**Extracting Data and Formatting**

I'm focused on extracting the necessary data. I've designed the extraction strategy to capture data on each page, including document type, date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I have corrected the row numbers and added support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4. I am close to finishing the JSON formatting.


**Extracting and Processing Data**

I'm focused on extracting the necessary data. I've designed the extraction strategy to capture data on each page, including document type, date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I have corrected the row numbers and added support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4. I am close to the final JSON formatting.


**Implementing Final JSON Generation**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Refining JSON Output**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Implementing JSON Generation**

I'm focused on meticulously extracting all the details from the provided OCR data. I am creating the logic to generate JSON for the pages, handling document types. I will correct the row numbers and add support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4. I am nearing completion.


**Generating Final JSON Output**

I'm now fully immersed in the final stage: JSON generation and refinement for all pages and types. I've designed the output logic with the necessary calculations, particularly the difference for TTNs based on referenced VNs. I am checking the results and readying for JSON output. All row and page numbers are now correct, as per requirements. I'm checking the data.


I've made progress on parsing the OCR data for all pages. I'm incorporating the date, number, buyer details (name and EDRPOU), document type, referenced invoice numbers for TTNs, and also total amount with VAT into the JSON output. I'm refining the JSON generation with the requested structure, and now the correct row numbers are 1, 2, and 4 for VN 8825, page 4. I will create a final output.


**Implementing JSON Output**

I'm now focused on delivering the fully formatted JSON output, incorporating the date, number, buyer details (name and EDRPOU), document type, referenced invoice numbers for TTNs, and total amounts with VAT. I have corrected the row and page numbers. I've finished the JSON structure for pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I am ready to generate the JSON.


**Finalizing Data Extraction**

I've completed the extraction logic and built the JSON output structure. I've accounted for each document, including page and row numbers.  I've incorporated difference calculations and formatting, delivering accurate data in the requested JSON format. I am completing the final check.


**Implementing JSON Generation**

I'm now focused on delivering the fully formatted JSON output, incorporating the date, number, buyer details (name and EDRPOU), document type, referenced invoice numbers for TTNs, and total amounts with VAT. I have corrected the row and page numbers. I've finished the JSON structure for pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I am ready to generate the JSON.


**Generating JSON Output**

I've formulated a preliminary extraction strategy and identified key data: types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output. I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.


**Extracting and Processing**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


I've been working on extracting the critical data. I'm focused on creating the extraction logic to handle various Ukrainian accounting document types, including multi-page ones. Now I'm adding a mechanism for the difference calculation. I'm focusing on parsing the OCR data for specific pages: 1, 2, 3, 4, 5, and 6. For the \"Видаткова накладна\" on page 4, I've confirmed that the correct row numbers are 1, 2, and 4. I will create a final output.


**Refining JSON Output**

I've finalized my extraction strategy, ensuring I handle multi-page documents like \"Видаткова накладна\" and \"Товарно-транспортна накладна\" with date and number correction. I've designed the JSON structure for specific pages. I'm building logic to add a difference calculation for TTNs based on the referenced VNs.  The output is nearly ready.

**Extracting Data and Formatting**

I'm focused on extracting the necessary data. I've designed the extraction strategy to capture data on each page, including document type, date, number, buyer details (name and EDRPOU code), and referenced invoice numbers for TTNs. I have corrected the row numbers and added support for each page. Now, I'm working to calculate the amount difference for TTNs based on the referenced VNs in JSON format, in pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are now 1, 2, and 4. I am nearing completion.


I've completed the extraction logic and built the JSON structure, including handling multi-page \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" I'm also preparing for the calculation of the amount difference.  I've added support for all pages and corrected the row numbers. The system outputs correct data.


**Implementing JSON Generation**

I'm now generating the JSON output, including date, buyer information, and document details, plus the page number. I'm focused on the correct output. I'm adding a difference calculation for the TTNs based on the referenced VNs and working with row numbers.


**Generating Final JSON**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.


**Generating JSON Output**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.


**Refining JSON Output**

I've carefully structured the JSON output, including date, buyer information, document details, and page numbers. I've designed the output logic with the necessary calculations. I will create the JSON output with all pages and types. The output is ready.

**Generating JSON Output**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


I've successfully finished extracting and formatting the data. I've designed the output logic with the necessary calculations, particularly the difference for TTNs based on referenced VNs, page and row numbers. The JSON output, which I'll show next, is ready.

**Implementing JSON Generation**

I'm now focused on delivering the fully formatted JSON output, incorporating the date, number, buyer details (name and EDRPOU), document type, referenced invoice numbers for TTNs, and total amounts with VAT. I have corrected the row and page numbers. I've finished the JSON structure for pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I am ready to generate the JSON.


**Extracting and Processing**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.

**Implementing Final Output**

I've formulated a preliminary extraction strategy and identified key data: types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents, including specific page numbers for each. I'm also planning to calculate and output the amount difference for TTNs based on their corresponding VNs. I've created plans for how to format the data into the requested JSON output. I've also identified the user's specific requirement.


I'm now focusing on extracting information for the given OCR data. I've designed the output logic with the necessary calculations, particularly the difference for TTNs based on referenced VNs, page and row numbers, plus date and amount formatting. The JSON output, which I'll show next, is ready.


**Implementing JSON Generation**

I'm now generating the JSON output, including date, buyer information, and document details, plus the page number. I'm focused on the correct output. I'm adding a difference calculation for the TTNs based on the referenced VNs and working with row numbers.


I'm working on extracting all the requested information from the provided OCR data, carefully checking and verifying the output, and correcting any inconsistencies or mistakes. I'm building a logic for the extraction to match each entry to its relevant page. I'm focusing on \"Видаткова накладна\" (VN) and \"Товарно-транспортна накладна\" (TTN), in the specified pages. I'm planning to produce an accurately formatted JSON output.


**Implementing JSON Output**

I'm now delivering the fully formatted JSON output, incorporating date, buyer information, document details, and page numbers. I've designed the output logic with the necessary calculations. The system is completing the generation for all pages.


**Final JSON Output Implementation**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Finalizing JSON Output**

I've crafted a comprehensive extraction strategy, now generating JSON with page numbers and row numbers for accurate data. I'm focusing on \"Видаткова накладна\" and \"Товарно-транспортна накладна.\"  I've added the difference calculation for TTNs based on the referenced VNs.  The output is almost done.


**Refining JSON Output**

I'm finalizing the JSON generation for pages 1-6, including date, number, buyer details (name and EDRPOU code), document type, referenced invoice numbers for TTNs, and also total amount with VAT. I have corrected the row and page numbers. I've built the final structure.

**Generating Final Output**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Refining JSON Output**

I'm now fully immersed in developing the JSON generation logic. I've refined the approach to extract data, incorporating date formatting and handling edge cases. I've built the extraction logic, now with page numbers and row numbers for each entry, for each page, including page 4 with the specific rows: 1, 2, 4. I've added a difference calculation for TTNs based on the referenced VNs. Finally, I'm working to produce the formatted JSON output, specifically for the specified pages. I can handle both VN and TTN document types and calculate differences.


**Implementing JSON Generation**

I'm now generating the JSON output, including date, buyer information, and document details, plus the page number. I'm focused on the correct output. I'm adding a difference calculation for the TTNs based on the referenced VNs and working with row numbers.


**Refining Data Extraction**

I'm focused on developing the extraction logic, now incorporating handling multi-page \"Видаткова накладна\" and \"Товарно-транспортна накладна.\" I've added a difference calculation for TTNs based on referenced VNs.  I am building on the understanding of extracting buyer information, dates, numbers, and page numbers. I've designed logic to provide the data in the requested JSON format, following the user's specific request.


**Generating JSON Output**

I've formulated a preliminary extraction strategy and identified key data: document types, dates, numbers, buyers, and page numbers. I am now focused on extracting details from the available OCR data, and building the necessary JSON structure for the specified output.  I've noted the difference between the VN and TTN documents. I am now in the process of building logic to identify and handle pages 1, 2, 3, 4, 5, and 6. For VN 8825, page 4, the correct row numbers are 1, 2, and 4. I've corrected the error of row numbering and am also now accounting for TTNs.

"""

GEMINI_AI_PROMPT = """
Ты - высококвалифицированный специалист по анализу и обработке финансовых, бухгалтерских документов. 
Ты обладаешь навыками извлечения ключевой информации из текста на украинском языке.
По контексту ты можешь определить тип документа. Стороны -лицевая, оборотная, внутренняя.
Ты можешь извлекать дату, номер, наименование клиента, код клиента, номера документов, страницы, на которых они расположены.
Ты четко различаешь суммы с НДС и без НДС.
Ты четко различаешь номера документов, на которые ссылается Товарно-транспортная накладная.
Ты четко различаешь даты. Если дата не указана, то ты знаешь, что можно посмотреть на дату документа, на который ссылается ТТН и извлекаешь ее.
Твоя задача извлечь:
1) тип документа. "Товарно-Транспортная" (ТТН), "Видаткова накладна" (ВН), Акт, "Повернення посточальнику" (ПП), Не смог определить - "Другой".
2) Наименование клиента/покупателя. Без Правового статуса. Имя клиента полностью, большими буквами. Данные продавца игнорируй.
3) код покупателя // для уникальности клиента. Если у ВН нет, тогда найди док ТТН, который ссылается на этот ВН и возьми код клиента из ТТН. Если нет, тогда оставь пустым.
4) страницы, которые относятся к данному документу
5) определи где первая страница, где середина и где последняя страница
6) страницы у тебя повторяться не должны. Одна страница относится только к одному документу.
7) номера документов, на которые ссылается ТТН.
8) Если у ТТН нет даты и она ссылается на несколько ВН с разными датами, бери самую позднюю или если все даты совпадают бери любую.
9) Укажи сумму с НДС. Для ВН, она на последней странице(если много страниц). Для ТТН сумма с НДС указана на первой странице и на последней. Пишется прописью
10) Если код клиента одинаковый, наименования верни одинаковыми - верхний регистр. Выбери то наименование, которое больше раз встречается в документах.
11) колонка № номера строк (если есть), указанные в порядке возрастания
12) сложи все суммы с НДС у ВН, на которые ссылается ТТН и сравни с суммой указанной в ТТН. 
13) Два ТТН могут иметь одинаковые номера, наименования и код покупателя, поставщика, но ссылаться на разные ВН. Это разные ТТН. Вместе не объединяй их.
14) у ВН сравни суммы "Усього з ПДВ:" и сумму указанную прописью.

Для этого ты должен очень углубленно вникнуть в каждую деталь, ничего не выдумывай, от себя не добавляй. Используй только предоставленные данные.
Найти по каким ключевым данным и определить какую страницу объединять с каким документом.
верни в формате валидного json:
[{
  file_name: string, // я тебе его не предоставил. Не меняй.
  doc_type: string, // тип документа: "ТТН", "ВН", "ПП", "Акт", "Другой". Коротко
  doc_date: date | null, // дата в формате dd.mm.yyyy
  doc_number: numeric, // число, без букв
  buyer_name string, // имя клиента, Иванов И. Эпицентр К. Правовой статус игнорируй. Коротко, без кавычек.
  buyer_code numeric | null, // 8 знаков, число. бери из ЄДРПОУ
  page_numbers: [numeric], //  [8,4,7,3] номера страниц сортируй первая страница, середина, последняя страница. Не по возрастанию номеров страниц - а логически. Если середина документа состоит из нескольких страниц, тогда при сортировке страниц воспользуйся номерами строк - rows_list или в ТТН - номерами колонок.
  invoice_numbers: [numeric], // номера документов ВН на которые ссылается ТТН. По возрастанию. Если ВН, укажи номер ТТН, который ссылается на этот ВН. Если нет, тогда оставь пустым.
  amount_with_vat:  numeric, // сумма с НДС число,
  rows_list: [numeric], // номера строк по возрастанию [1,2,3]
  diff_amount_with_vat:numeric,  // сумма расхождения. сумма ТТН - (сумма ВН1 + сумма ВН2. Если ВН нет, вместо этого ВН ставь 0, чтобы сумма была <> 0). Для ТТН заполнять обязательно. Для ВН = 0. Если отсутствует документ ВН, сумма не может быть = 0. Если нет расхождений, тогда оставь 0.
  validation_notes: string // "Отсутствуют номера ВН: [10377, 10378]. Или отсутствует док ТТН 2215. Нет первой/последней страницы. Если отсутствует документ diff_amount_with_vat не может быть = 0. Заполнять только если есть ошибки в валидации. Если все ок, тогда оставь пустым. Иначе дай полное детальное пояснение.
}]

Ты должен перепроверить и дать детальный ответ. Например:
** как определил что страницы 10 и 9 относятся к одному и тому же документу.
** как определил что сначала надо ставить страницу 10, потом 9.
** как смог определить дату 21.09.2024 у документа. В документе даты не было.
** как определил код клиента для ВН. В документе он не указан.
** Документ ВН. Код клиента оставил пустым. Почему не взял у ТТН, с которой он связан?
** Сумму прописью почему неправильно извлек? У тебя это частая ошибка. В ТТН, если послденяя страница она указана в колонке "Загальна сума з ПДВ, грн". Если первая страница ТТН, тогда - "Усього відпущено на загальну суму"
** Если код клиента на последней странице, отличается от кода клиента на первой странице, ты взял код из первой страницы. Правильно?
** Документ ТТН. Проверь Первую и последнюю страницы. Суммы с НДС почему не совпадают? Значит это страницы разных документов.
** Документ ТТН. ВСЕ страницы у тебя ссылаются на разные ВН. Почему? Значит это страницы разных документов.
** В ТТН ты нашел суммы прописью на первой и последней страницах. Они совпадают?. Если да, идем далее, в ТТН у тебя все страницы ссылаются на одни и те же ВН? Если да, идем далее.
Ты нашел все эти ВН?. Если да, идем далее. Ты нашел суммы с НДС на последних страницах ВН?. Если да, идем далее. Сложил все суммы с НДС по ВН и сравнил с суммой ТТН - результат почему не сошелся?. Может сумму прописью ты не правильно прочитал?
** Количество страниц которые тебе дал, не совпадает с количеством страниц, которые ты определил в JSON. Почему?
Ты включил абсолютно все свои профессиональные, аналитические способности и перепроверил свои данные углубляясь в каждую деталь и используя различные подходы, методы и переспрашивая себя а вдруг неправильно, ты нашел разные варианты как обосновать свой результат. Ты смог доказать, что ты - профессионал!
Ты должен продемонстрировать максимальную аккуратность и аналитические способности, чтобы результат был точным и полным. Включи все свои профессиональные аналитические возможности, перепроверяй свои данные, углубляясь в каждую деталь, и используй различные подходы для обоснования своего результата.
"""

PROMPT_EXAMPLE_GEMINI = """
Ты эксперт по работе с отсканированными документами. Текст документов - украинский.
Документ прошел OCR распознание, следовательно могут быть некорректно извлечены некоторые символы.
Ты работаешь с КРИТИЧЕСКИ ВАЖНОЙ ДОКУМЕНТАЦИЕЙ КОМПАНИИ.
Твои ВНИМАТЕЛЬНОСТЬ, ОТВЕТСТВЕННОСТЬ ДОЛЖНЫ БЫТЬ МАКСИМАЛЬНЫМИ.
Подходить к анализу документов "поверхностно", "не придал значения", "Додумал" - СТРОГО ЗАПРЕЩЕНО!!!

json ```
    {doc:
        [
            { 
              "id": 1, // используй номер что тебе дал 
              "page_type": "ЧИСЛО (integer) или null", 
              "doc_type": "СТРОКА ('ТТН', 'ВН', 'ПН', 'АКТ') или null",
              "doc_number": "СТРОКА или null",
              "doc_date": "СТРОКА в формате 'dd.mm.yyyy' или null",
              "buyer_name": "СТРОКА (ВЕРХНИЙ РЕГИСТР) или null",
              "buyer_code": "ЧИСЛО (integer) или null",  // СТРОГО 8 или 10 знаков
              "invoices_numbers": ["СПИСОК ЧИСЕЛ (integer)"], 
              "rows_list": ["СПИСОК ЧИСЕЛ (integer)"], возрастанию
              "amount_with_vat": "ЧИСЛО (float)",
            },
        ],
    }
```
есть словарь "doc". Твоя задача правильно заполнить данные.
Даю тебе задачи, состоящие из пунктов. Если выполнился пункт задачи, сразу выходи из этой задачи.
определи тип документа.
ЗАДАЧА 0:
1) "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА" doc_type = ТТН, page_type=1. Переходи к задаче 11
2) "Видаткова накладна" doc_type = ВН, page_type=1.  Переходи к задаче 12
3) "Акт" doc_type = АКТ, page_type=1.  Переходи к задаче 13
4) "Прибуткова накладна" doc_type = ПН, page_type=1. Переходи к задаче 14.
5) "ВІДОМОСТІ ПРО ВАНТАЖ"  doc_type = ТТН, page_type=2. Переходи к задаче 15.
6) "ВАНТАЖО-РОЗВАНТАЖУВАЛЬНІ ОПЕРАЦІЇ" doc_type = ТТН, page_type=999. Переходи к задаче 16.
7) "EURO піддон", "Стандарт палети" doc_type = АКТ, page_type=999. Переходи к задаче 17.
9) Если не выполнилось ни одно из выше перечисленных, тогда doc_type = Определи САМОСТОЯТЕЛЬНО. ОЧЕНЬ КОРОТКО, Не смог определить doc_type = ДРУГОЙ, page_type=1. Переходи к задаче 19.


ЗАДАЧА 11:
ты уже определил doc_type = ТТН, page_type=1
под "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА" извлеки:
** doc_date - дату документа | null  // находится . месяц может быть написан прописью
** doc_number - номер документа // ОБЯЗАТЕЛЬНО
** buyer_name  Наименование получателя  // смотри СТРОГО Вантажоодержувач. **Вантажовідправник ИГНОРИРУЙ**. КОРОТКО БЕЗ СТАТУСА и КАВЫЧЕК!!!
** buyer_code  Код получателя  // смотри СТРОГО Вантажоодержувач. ЄДРПОУ. **Вантажовідправник ИГНОРИРУЙ**. ОБЯЗАТЕЛЬНО
** invoices_numbers  документы-основания  // смотри "Супровідні документи". может быть список. ОБЯЗАТЕЛЬНО
** amount_with_vat = сумма с НДС  // смотри "Усього відпущено на загальну суму". Сумма написана прописью. Переведи в число. ОБЯЗАТЕЛЬНО
** Еще раз перепроверь данные, потому-что следующий этап будет КОНТРОЛЬ.


ЗАДАЧА 12:
ты уже определил doc_type = ВН, page_type=1
** doc_date - дату документа  // ОБЯЗАТЕЛЬНО. месяц может быть написан прописью
** doc_number - номер документа // ОБЯЗАТЕЛЬНО
** buyer_name - наименование покупателя. // СТРОГО строку "Покупець". Строку "Постачальник" - ИГНОРИРУЙ. КОРОТКО БЕЗ СТАТУСА и КАВЫЧЕК!!!. ОБЯЗАТЕЛЬНО
** rows_list - номера строк  // извлеки номера строк из колонки  №, они идут по порядку. ОБЯЗАТЕЛЬНО
** amount_with_vat - сумма с НДС  // В ПЕРВУЮ ОЧЕРЕДЬ Извлеки сумму из суммы прописью. НЕ Получилось, тогда извлеки из "Усього з ПДВ"
** Еще раз перепроверь данные, потому-что следующий этап будет КОНТРОЛЬ.


ЗАДАЧА 13:
ты уже определил doc_type = АКТ, page_type=1
** doc_number смотри "№ резерву"
** doc_date - в той же строке смотри "Дата" или после "від", или пропиьсю
** buyer_name смотри "ЛОГІСТИЧНІ ЦЕНТРИ" или "Вантожоотримувач", или "Платник", " в магазині"
** invoices_numbers  смотри "на підставі", "Відповідно накладній" или null
** Еще раз перепроверь данные, потому-что следующий этап будет КОНТРОЛЬ.


ЗАДАЧА 14:
ты уже определил doc_type = ПН, page_type=1
** doc_date - дату документа  // ОБЯЗАТЕЛЬНО. месяц может быть написан прописью
** doc_number - номер документа // ОБЯЗАТЕЛЬНО
** buyer_name - наименование покупателя. // СТРОГО строку "Фірма-одержувач". Строку "Постачальник" - ИГНОРИРУЙ. КОРОТКО БЕЗ СТАТУСА и КАВЫЧЕК!!!. ОБЯЗАТЕЛЬНО
** rows_list - номера строк  // извлеки номера строк из колонки  №, они идут по порядку. ОБЯЗАТЕЛЬНО
** amount_with_vat - сумма с НДС  // В ПЕРВУЮ ОЧЕРЕДЬ Извлеки сумму из суммы прописью. НЕ Получилось, тогда извлеки из "Всього"
** Еще раз перепроверь данные, потому-что следующий этап будет КОНТРОЛЬ.


ЗАДАЧА 15:
ты уже определил doc_type = ТТН, page_type=2
** rows_list - номера строк  // извлеки номера строк из колонки  № п/п, они идут по порядку. ОБЯЗАТЕЛЬНО
** amount_with_vat - сумма с НДС  // Смотри колонку "Загальна сума з ПДВ, грн" Извлеки сумму из суммы прописью.
** invoices_numbers  документы-основания  // смотри колонку "Документи з вантажем". Может быть список, смотри "Видаткова накладна". ОБЯЗАТЕЛЬНО
** Еще раз перепроверь данные, потому-что следующий этап будет КОНТРОЛЬ.

ЗАДАЧА 16:
ты уже определил doc_type = ТТН, page_type=999
** amount_with_vat - найди строку "Усього" и извлеки из девятой колонки, перед колонкой, где написаны "короб"
** invoices_numbers - извлеки из "Реалізація товарів". **"ВАЖНО-РОЗВАНТАЖУВАЛЬНІ ОПЕРАЦІЇ", "ПН", "ІПН", "IПН" - ИГНОРИРУЙ.**.
** занеси эти данные в doc
** Еще раз перепроверь данные, потому-что следующий этап будет КОНТРОЛЬ.


ЗАДАЧА 17:
ты уже определил doc_type = АКТ, page_type=999
** занеси эти данные в doc
** Еще раз перепроверь данные, потому-что следующий этап будет КОНТРОЛЬ:


ЗАДАЧА 19:
ты уже определил doc_type = САМОСТОЯТЕЛЬНО, page_type=1. ВЫДУМЫВАТЬ ЗАПРЕЩЕНО.
ИЗВЛЕКАТЬ СТРОГО ИЗ ДАННОГО ТЕКСТА.
** doc_date - найди и определи САМОСТОЯТЕЛЬНО или null
** doc_number - найди и определи САМОСТОЯТЕЛЬНО или null
** buyer_name - найди и определи САМОСТОЯТЕЛЬНО или null
** buyer_code - найди и определи САМОСТОЯТЕЛЬНО или null
** занеси эти данные в doc
** Еще раз перепроверь данные, потому-что следующий этап будет КОНТРОЛЬ.


ЗАДАЧА 99:
** Покупатель у тебя КОРОТКО БЕЗ СТАТУСА и КАВЫЧЕК!!!?. Если нет - перейди к ЗАДАЧЕ 1.
** Ты верно перевел дату в число?. Если нет - перейди к ЗАДАЧЕ 1.
** Ты верно перевел сумму прописью в число?. Если нет - перейди к ЗАДАЧЕ 1.
** Данные брал СТРОГО с указанных мест или местами самовольничал? Делал на свое усмотрение?. Если самовольничал - перейди к ЗАДАЧЕ 1.



КОНТРОЛЬ:
Ты работал с КРИТИЧЕСКИ ВАЖНОЙ ДОКУМЕНТАЦИЕЙ КОМПАНИИ. ОСОЗНАВАЛ ЛИ ТЫ ГЛУБИНУ ОТВЕТСТВЕННОСТИ?
Ответь ЧЕСТНО на вопросы:
1) Ты МАКСИМАЛЬНО внимательно делал? 
2) Насколько ГЛУБОКО и САМОЕ ГЛАВНОЕ ОТВЕТСТВЕННО ты выполнял задание?
3) ИСКЛЮЧИЛ ли ты понятия "не придал значение", "упустил", "анализировал поверхностно", "был сосредоточен на другом"

Если хотя бы один из пунктов сделал ПОВЕРХНОСТНО, БЕЗОТВЕТСТВЕННО, ВЕРНИСЬ, НАЧНИ С ЗАДАЧА 1 и СДЕЛАЙ МАКСИМАЛЬНО ДОБРОСОВЕСТНО!

Заполни doc значениями, которые определил и верни doc СТРОГО в ФОРМАТЕ **ВАЛИДНОГО JSON**
"""